<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array_replace_recursive(require __DIR__.'/en.php', [
    'first_day_of_week' => 0,
    'meridiem' => ['KI', 'UT'],
    'weekdays' => ['<PERSON><PERSON><PERSON>', 'Njumatatu', 'N<PERSON><PERSON><PERSON>', 'N<PERSON><PERSON><PERSON>', '<PERSON><PERSON>i', 'N<PERSON><PERSON><PERSON>', 'NJumamothii'],
    'weekdays_short' => ['Kma', 'Tat', 'Ine', 'Tan', 'Arm', 'Maa', 'NMM'],
    'weekdays_min' => ['Kma', 'Tat', 'Ine', 'Tan', 'Arm', 'Maa', 'NMM'],
    'months' => ['Mweri wa mbere', 'Mweri wa kaĩri', '<PERSON>weri wa kathatũ', '<PERSON>weri wa kana', '<PERSON>weri wa gatano', '<PERSON>weri wa gatantatũ', '<PERSON>weri wa mũgwanja', '<PERSON>weri wa kanana', '<PERSON>wer<PERSON> wa kenda', '<PERSON>weri wa ikũmi', '<PERSON>weri wa ikũmi na ũmwe', 'Mweri wa ikũmi na Kaĩrĩ'],
    'months_short' => ['Mbe', 'Kai', 'Kat', 'Kan', 'Gat', 'Gan', 'Mug', 'Knn', 'Ken', 'Iku', 'Imw', 'Igi'],
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'DD/MM/YYYY',
        'LL' => 'D MMM YYYY',
        'LLL' => 'D MMMM YYYY HH:mm',
        'LLLL' => 'dddd, D MMMM YYYY HH:mm',
    ],
]);
