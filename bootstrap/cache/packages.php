<?php return array (
  'barryvdh/laravel-ide-helper' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    ),
  ),
  'laravel-lang/config' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Config\\ServiceProvider',
    ),
  ),
  'laravel-lang/lang' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Lang\\ServiceProvider',
    ),
  ),
  'laravel-lang/locales' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Locales\\ServiceProvider',
    ),
  ),
  'laravel-lang/publisher' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Publisher\\ServiceProvider',
    ),
  ),
  'laravel/pail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Pail\\PailServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/scout' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Scout\\ScoutServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'orchid/blade-icons' => 
  array (
    'providers' => 
    array (
      0 => 'Orchid\\Icons\\IconServiceProvider',
    ),
  ),
  'orchid/platform' => 
  array (
    'aliases' => 
    array (
      'Alert' => 'Orchid\\Support\\Facades\\Alert',
      'Dashboard' => 'Orchid\\Support\\Facades\\Dashboard',
    ),
    'providers' => 
    array (
      0 => 'Orchid\\Platform\\Providers\\FoundationServiceProvider',
    ),
  ),
  'pestphp/pest-plugin-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Pest\\Laravel\\PestServiceProvider',
    ),
  ),
  'tabuna/breadcrumbs' => 
  array (
    'aliases' => 
    array (
      'Breadcrumbs' => 'Tabuna\\Breadcrumbs\\Breadcrumbs',
    ),
    'providers' => 
    array (
      0 => 'Tabuna\\Breadcrumbs\\BreadcrumbsServiceProvider',
    ),
  ),
  'watson/active' => 
  array (
    'aliases' => 
    array (
      'Active' => 'Watson\\Watson\\Facades\\Active',
    ),
    'providers' => 
    array (
      0 => 'Watson\\Active\\ActiveServiceProvider',
    ),
  ),
);