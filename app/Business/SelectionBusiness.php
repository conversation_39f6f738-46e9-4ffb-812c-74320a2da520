<?php

namespace App\Business;

use App\Exports\SelectionExport;
use App\Models\File;
use App\Models\SelectionOption;
use App\Models\SelectionRecord;
use App\Services\OssStorage;
use DB;
use Maatwebsite\Excel\Facades\Excel;
use Storage;
use Str;

class SelectionBusiness
{
    // 主体型号选择
    static protected function generateMainData(array $codeParts): array
    {
        // 用于判断的部分 code
        $codes = array_filter(
            array_slice($codeParts, 0, count($codeParts) == 8 ? 4 : 5),
            fn($code) => $code !== 'w'
        );

        $code = trim(implode('-', $codes));

        $result = SelectionOption::where([
            'type' => SelectionOption::MAIN_TYPE,
            'model' => $code,
        ])->get();

        return self::formatDataToRow($result);
    }

    // 电磁阀片
    static protected function generateElectromagneticData(array $codeParts): array
    {
        // 倒数第四段
        $code = trim(array_slice($codeParts, -4, 1)[0]);

        preg_match_all('/(\d+)([A-Z]+)/', $code, $matches, PREG_SET_ORDER);

        $noList = [];
        foreach ($matches as [$full, $count, $key]) {
            $noList[$key] = ($noList[$key] ?? 0) + (int)$count;
        }

        $result = SelectionOption::where([
            'type' => SelectionOption::ELECTROMAGNETIC_TYPE,
        ])->whereIn('no', array_keys($noList))
            ->get()
            ->each(function (SelectionOption $opt) use ($noList) {
                $opt->num = $noList[$opt->no] ?? 1;
            });

        return self::formatDataToRow($result);
    }

    // 阀接头
    static protected function generateValveData(array $codeParts): array
    {
        $code = trim(array_slice($codeParts, -1, 1)[0]);
        // 位数
        $digits = trim(array_slice($codeParts, -5, 1)[0]);

        $result = SelectionOption::where([
            'type' => SelectionOption::VALVE_TYPE,
            'no' => $code,
        ])->get()->each(function (SelectionOption $opt) use ($digits) {
            $opt->num = (int)$digits * 2;
        });

        return self::formatDataToRow($result);
    }

    // 消声器
    static protected function generateSilencerData(array $codeParts): array
    {
        $code = trim(array_slice($codeParts, -2, 1)[0]);

        $result = SelectionOption::where([
            'type' => SelectionOption::SILENCER_TYPE,
            'no' => $code,
        ])->get();

        return self::formatDataToRow($result);
    }

    // 阀岛进气接头&堵头型号
    static protected function generateHeadData(array $codeParts): array
    {
        $code = trim(array_slice($codeParts, -3, 1)[0]);

        $result = SelectionOption::where([
            'type' => SelectionOption::HEAD_TYPE,
            'no' => $code,
        ])->get();

        return self::formatDataToRow($result);
    }

    static protected function formatDataToRow(\Illuminate\Database\Eloquent\Collection $data): array
    {
        $result = [];
        $data->each(function (SelectionOption $opt) use (&$result) {
            $result[] = [
                $opt->sn,
                $opt->model,
                $opt->feature,
                $opt->num,
            ];
        });
        return $result;
    }

    // 基于 Code 码生成 Excel
    static public function generateExcelByCode(string $code): string
    {

        // 8-9位
        $codeParts = explode('-', $code);

        $rows = [
            ...self::generateMainData($codeParts),
            ...self::generateHeadData($codeParts),
            ...self::generateSilencerData($codeParts),
            ...self::generateValveData($codeParts),
            ...self::generateElectromagneticData($codeParts),
        ];

        $headers = ['物料号', '型号', '功能特性', '数量'];
        $fileName = "{$code}.xlsx";
        $relativePath = 'tmp/' . Str::random(8) . '-' . $fileName;
        $filePath = Storage::path($relativePath);

        try {

            // 确保目录存在
            Storage::makeDirectory('tmp');

            // 保存到本地临时目录
            Excel::store(new SelectionExport($rows, $headers), $relativePath, 'local');

            // 上传到 OSS
            $oss = new OssStorage();
            $url = $oss->put($fileName, $filePath);

            // 存库
            DB::transaction(function () use ($fileName, $url, $code) {
                $file = File::create([
                    'name' => $fileName,
                    'ext' => 'xlsx',
                    'url' => $url,
                    'type' => File::EXCEL
                ]);
                SelectionRecord::create([
                    'code' => $code,
                    'file_id' => $file->id,
                    'user_id' => \Auth::user()->id,
                ]);
            });

            return $url;

        } finally {
            @unlink($filePath);
        }
    }
}
