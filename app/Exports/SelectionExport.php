<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class SelectionExport implements FromArray, WithHeadings
{
    protected array $data;
    protected array $headings;

    public function __construct(array $data, array $headings)
    {
        $this->data = $data;
        $this->headings = $headings;
    }

    // 返回数据
    public function array(): array
    {
        return $this->data;
    }

    // 返回表头
    public function headings(): array
    {
        return $this->headings;
    }
}
