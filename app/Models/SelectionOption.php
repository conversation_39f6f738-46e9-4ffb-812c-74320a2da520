<?php

namespace App\Models;

/**
 * @property int $id
 * @property int $type 类型:1-主体2-阀岛进气接头&堵头3-消声器型号4-阀接头5-电磁阀阀片
 * @property string $no 编号
 * @property string $sn 物料号
 * @property string $model 型号
 * @property string|null $feature 功能特性
 * @property int $num 数量
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption whereFeature($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption whereModel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption whereNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption whereSn($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SelectionOption whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SelectionOption extends Base
{
    protected $table = 'selection_options';

    const
        MAIN_TYPE = 1,
        HEAD_TYPE = 2,
        SILENCER_TYPE = 3,
        VALVE_TYPE = 4,
        ELECTROMAGNETIC_TYPE = 5;
    const TYPES = [
        self::MAIN_TYPE => '主体',
        self::HEAD_TYPE => '阀岛进气接头&堵头',
        self::SILENCER_TYPE => '消声器型号',
        self::VALVE_TYPE => '阀接头',
        self::ELECTROMAGNETIC_TYPE => '电磁阀阀片',
    ];

    // 根据类型获取选项
    static function getOptsByType(int $type): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('type', $type)->get();
    }
}
