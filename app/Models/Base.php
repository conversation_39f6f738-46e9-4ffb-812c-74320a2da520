<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Orchid\Filters\Filterable;
use Orchid\Screen\AsSource;

/**
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Base newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Base newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Base query()
 * @method static filters()
 * @mixin \Eloquent
 */
class Base extends Model
{
    use HasFactory, AsSource, Filterable;

    protected $guarded = [];
    protected array $allowedSorts = [
        'id',
        'created_at',
        'updated_at',
    ];
}
