<?php

namespace App\Services;

use AlibabaCloud\Oss\V2 as Oss;
use AlibabaCloud\Oss\V2\Credentials\Credentials;
use AlibabaCloud\Oss\V2\Models\PutObjectRequest;

class OssStorage
{
    protected Oss\Client $client;
    protected string $bucket;

    public function __construct(string $bucket = 'solidotech')
    {
        $credentialsProvider = new Oss\Credentials\CredentialsProviderFunc(function () {
            return new Credentials(
                config('filesystems.disks.oss.access_id'),
                config('filesystems.disks.oss.access_key'),
            );
        });

        $cfg = Oss\Config::loadDefault();
        $cfg->setRegion( config('filesystems.disks.oss.region'));
        $cfg->setCredentialsProvider($credentialsProvider);
        $this->client = new Oss\Client($cfg);
        $this->bucket = $bucket;
    }

    public function put(string $fileName, string $filePath): string
    {

        $ossPath = 'tools/' . md5($filePath) . '/' . $fileName;

        $req = new PutObjectRequest(bucket: $this->bucket, key: $ossPath);
        $req->body = Oss\Utils::streamFor(fopen($filePath, 'r'));
        $this->client->putObject($req);

        return config('filesystems.disks.oss.url') . '/' . $ossPath;
    }
}
