<?php

namespace App\Orchid\Screens\ProductSelection;

use App\Models\SelectionOption;
use Illuminate\Http\Request;
use Orchid\Screen\Actions\Button;
use Orchid\Screen\Actions\ModalToggle;
use Orchid\Screen\Fields\Group;
use Orchid\Screen\Fields\Input;
use Orchid\Screen\Fields\Select;
use Orchid\Screen\Fields\TextArea;
use Orchid\Screen\Screen;
use Orchid\Screen\TD;
use Orchid\Support\Color;
use Orchid\Support\Facades\Layout;

class SelectOptionsScreen extends Screen
{
    protected string $name = '参数管理';
    protected string $description = '配置用于选型文档导出时所参照的参数';

    public function commandBar(): iterable
    {
        return [
            ModalToggle::make('添加')
                ->modal('saveModal')
                ->method('create')
                ->icon('plus'),
        ];
    }

    public function layout(): iterable
    {
        /** @var string|string[] $modalRows */
        $modalRows = Layout::rows([
            Input::make('option.id')->hidden(),
            Select::make('option.type')
                ->title('类型')
                ->options(SelectionOption::TYPES)
                ->required(),
            Input::make('option.no')
                ->title('编号')
                ->placeholder('请输入编号'),
            Input::make('option.sn')
                ->title('物料号')
                ->placeholder('请输入物料号'),
            Input::make('option.model')
                ->title('型号')
                ->placeholder('请输入型号')
                ->required(),
            Input::make('option.num')
                ->type('number')
                ->title('数量')
                ->value(1)
                ->placeholder('请输入数量')
                ->required(),
            TextArea::make('option.feature')
                ->title('功能特性')
                ->placeholder('请输入功能特性'),
        ]);

        return [
            Layout::modal('saveModal', $modalRows)
                ->title('添加参数')
                ->applyButton(__('Save'))
                ->async('asyncGetOption'),

            Layout::table('option', [
                TD::make('id', 'ID'),
                TD::make('type', '类型')->render(function ($model) {
                    return SelectionOption::TYPES[$model->type] ?? '未知';
                }),
                TD::make('no', '编号')->render(fn($model) => $model->no ?: '-'),
                TD::make('sn', '物料号')->render(fn($model) => $model->sn ?: '-'),
                TD::make('model', '型号'),
                TD::make('num', '数量'),
                TD::make('feature', '功能'),
                TD::make('created_at', '创建时间')->sort(),
                TD::make('updated_at', '更新时间')->sort(),
                TD::make('Actions', '操作')
                    ->alignRight()
                    ->render(fn(SelectionOption $selectionOption) => Group::make([
                        ModalToggle::make('编辑')
                            ->type(Color::INFO)
                            ->modal('saveModal')
                            ->asyncParameters(['selectionOption' => $selectionOption])
                            ->method('update'),
                        Button::make('删除')
                            ->type(Color::DANGER)
                            ->confirm('删除后不可恢复，是否继续.')
                            ->method('delete', ['selectionOption' => $selectionOption->id]),
                    ])),
            ]),
        ];
    }

    protected function validateParams(Request $request): void
    {
        $request->validate([
            'option.type' => 'required|in:0,1,2,3,4,5',
            'option.no' => 'required|string|max:500',
            'option.sn' => 'nullable|string|max:500',
            'option.model' => 'required|string|max:500',
            'option.num' => 'required|numeric:|min:1',
            'option.feature' => 'nullable|string|max:500',
        ]);
    }

    public function create(Request $request): void
    {
        $this->validateParams($request);

        SelectionOption::create([
            'type' => $request->input('option.type'),
            'no' => $request->input('option.no'),
            'sn' => $request->input('option.sn'),
            'model' => $request->input('option.model'),
            'num' => $request->input('option.num'),
            'feature' => $request->input('option.feature'),
        ]);
    }

    public function asyncGetOption(SelectionOption $selectionOption): array
    {
        return [
            'option.id' => $selectionOption->id ?? null,
            'option.type' => $selectionOption->type ?? null,
            'option.no' => $selectionOption->no ?? null,
            'option.sn' => $selectionOption->sn ?? null,
            'option.model' => $selectionOption->model ?? null,
            'option. num' => $selectionOption->num ?? null,
            'option.feature' => $selectionOption->feature ?? null,
        ];
    }

    public function update(Request $request): void
    {
        $this->validateParams($request);

        SelectionOption::where('id', $request->input('option.id'))->update([
            'type' => $request->input('option.type'),
            'no' => $request->input('option.no'),
            'sn' => $request->input('option.sn'),
            'model' => $request->input('option.model'),
            'num' => $request->input('option.num'),
            'feature' => $request->input('option.feature'),
        ]);
    }

    public function delete(SelectionOption $selectionOption): void
    {
        $selectionOption->delete();
    }

    public function query(): iterable
    {
        return [
            'option' => SelectionOption::filters()->defaultSort('id', 'desc')->paginate(10),
        ];
    }
}
