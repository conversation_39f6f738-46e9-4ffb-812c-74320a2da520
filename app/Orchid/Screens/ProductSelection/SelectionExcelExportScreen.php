<?php

namespace App\Orchid\Screens\ProductSelection;

use Orchid\Screen\Actions\Button;
use Orchid\Screen\Fields\Group;
use Orchid\Screen\Fields\Input;
use Orchid\Screen\Screen;
use Orchid\Support\Color;
use Orchid\Support\Facades\Layout;

class SelectionExcelExportScreen extends Screen
{
    protected string $name = 'Excel 生成';
    protected string $description = '通过选型码生成 Excel 选型文档';

    public function query(): array
    {
        return [];
    }

    public function layout(): iterable
    {
        return [
            Layout::rows([
                    Input::make('record.code')
                        ->title('选型 Code')
                        ->type('text')
                        ->placeholder('请输入选型 Code')
                        ->required()
                        ->horizontal(),
                    Button::make('Submit')
                        ->method('buttonClickProcessing')
                        ->horizontal()
                        ->type(Color::BASIC),
            ])
        ];
    }
}
