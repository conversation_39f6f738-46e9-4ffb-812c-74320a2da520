<?php

namespace App\Orchid\Screens\ProductSelection;

use Alert;
use App\Business\SelectionBusiness;
use Illuminate\Http\Request;
use Orchid\Screen\Actions\Button;
use Orchid\Screen\Fields\Group;
use Orchid\Screen\Fields\Input;
use Orchid\Screen\Screen;
use Orchid\Support\Color;
use Orchid\Support\Facades\Layout;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class SelectionExcelExportScreen extends Screen
{
    protected string $name = 'Excel 生成';
    protected string $description = '通过选型码生成 Excel 选型文档';

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function query(): array
    {
        return [
            'url' => request()->get('url'),
            'record.code' => request()->get('code')
        ];
    }

    public function layout(): iterable
    {
        return [
            Layout::rows([
                Group::make([
                    Input::make('record.code')
                        ->title('选型 Code')
                        ->type('text')
                        ->placeholder('请输入选型 Code')
                        ->required()
                        ->horizontal(),
                    Button::make('Submit')
                        ->method('generateExcel')
                        ->horizontal()
                        ->type(Color::BASIC)
                        ->name("生成"),
                ])->fullWidth()->alignCenter(),
            ]),
            // 添加一个布局来显示生成的 URL
            Layout::view('orchid.selection-excel-url'),
        ];
    }

    public function generateExcel(Request $request)
    {
        $request->validate([
            'record.code' => 'required|string',
        ]);

        $code = $request->input('record.code');
        $url = SelectionBusiness::generateExcelByCode($code);

        return redirect()->route(
            'platform.product-selections.export',
            [
                'code' => $code,
                'url' => $url
            ]
        );
    }
}
