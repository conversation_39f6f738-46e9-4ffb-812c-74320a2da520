<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('selection_options', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('type')->nullable(false)->default(0)->comment('类型:1-主体2-阀岛进气接头&堵头3-消声器型号4-阀接头5-电磁阀阀片');
            $table->string('no', 100)->nullable(false)->default('')->comment('编号');
            $table->string('sn', 500)->nullable(false)->default('')->comment('物料号');
            $table->string('model', 500)->nullable(false)->default('')->comment('型号');
            $table->integer('num')->nullable(false)->default(1)->comment('数量');
            $table->text('feature')->nullable()->comment('功能特性');
            $table->timestamps();
            $table->index(['type', 'no']);
            $table->comment('选型参数选项');
        });

        Schema::create('files', function (Blueprint $table) {
            $table->id('id');
            $table->string('name', 200)->nullable(false)->default('')->comment('文件名');
            $table->string('ext', 50)->nullable(false)->default('')->comment('文件小写类型后缀');
            $table->string('url', 500)->nullable(false)->default('')->comment('下载地址');
            $table->tinyInteger('type')->nullable(false)->default(0)->comment('文件类型：0-未知1-选型excel');
            $table->timestamps();
            $table->comment('文件列表');
        });

        Schema::create('selection_records', function (Blueprint $table) {
            $table->id('id');
            $table->bigInteger('user_id')->nullable(false)->default(0)->comment('操作人uid');
            $table->string('code', 200)->nullable(false)->default('')->comment('选型编号');
            $table->bigInteger('file_id')->nullable(false)->default(0)->comment('相关附件');
            $table->timestamps();
            $table->comment('选型记录');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('selection_options');
        Schema::dropIfExists('files');
        Schema::dropIfExists('selection_records');
    }
};
